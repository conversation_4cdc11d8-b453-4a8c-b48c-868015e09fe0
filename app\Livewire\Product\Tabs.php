<?php

namespace App\Livewire\Product;

use App\Models\Product;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class Tabs extends Component
{
    use WithPagination;

    public Product $product;
    public $activeTab = 'description';

    // Review Form State
    public $rating = 5;
    public $comment = '';

    protected $rules = [
        'rating' => ['required', 'integer', 'min:1', 'max:5'],
        'comment' => ['required', 'string', 'min:10'],
    ];

    public function mount(Product $product)
    {
        $this->product = $product;
    }

    public function selectTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function submitReview()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        $this->validate();

        $this->product->reviews()->create([
            'user_id' => Auth::id(),
            'rating' => $this->rating,
            'comment' => $this->comment,
            'is_approved' => true // Or false, pending admin approval
        ]);

        // Reset form
        $this->reset(['rating', 'comment']);

        // Reset pagination to show the new review
        $this->resetPage();

        $this->dispatch('toast', message: 'Thank you for your review!', type: 'success');
    }

    public function render()
    {
        // Handle reviews pagination within the render method
        $reviews = $this->product->reviews()->with('user')->latest()->paginate(5, ['*'], 'reviews_page');

        return view('livewire.product.tabs', [
            'reviews' => $reviews,
        ]);
    }
}
