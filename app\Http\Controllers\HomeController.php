<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Vendor;
use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactFormMail;
use Illuminate\Support\Facades\Cache;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        // Set a 10-minute cache duration for all homepage queries to optimize performance.
        $cacheDuration = now()->addMinutes(10);

        // Cache the best-selling products query.
        $bestSellers = Cache::remember('home.bestSellers', $cacheDuration, function () {
            return Product::query()
                ->with(['category', 'vendor', 'brand']) // Eager load relationships
                ->where('is_best_seller', true)
                ->where('is_active', true)
                ->orderBy('updated_at', 'desc') // Order by when they were last marked as best seller
                ->limit(8)
                ->get();
        });

        // Cache the new arrivals query.
        $newArrivals = Cache::remember('home.newArrivals', $cacheDuration, function () {
            return Product::query()
                ->with(['category', 'vendor', 'brand']) // Eager load relationships
                ->where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->limit(8)
                ->get();
        });

        // Cache the featured categories query.
        $categories = Cache::remember('home.categories', $cacheDuration, function () {
            return Category::where('is_active', true)
                ->whereNull('parent_id')
                ->orderBy('order')
                ->limit(3)
                ->get();
        });

        // Cache the featured brands (vendors) query.
        $featuredBrands = Cache::remember('home.featuredBrands', $cacheDuration, function () {
            return Vendor::where('is_approved', true)
                ->where('is_featured', true)
                ->orderBy('created_at', 'desc')
                ->limit(3) // Limit to 3 as requested
                ->get()
                ->map(function ($vendor) {
                    return (object)[
                        'name' => $vendor->brand_name ?? $vendor->shop_name,
                        'logo_url' => $vendor->logo_url,
                        'slug' => $vendor->slug,
                        'type' => 'vendor',
                        'model' => $vendor
                    ];
                });
        });


        // Use static banner images for the slider
        $sliderBanners = [
            asset('storage/banner1.jpg'),
            asset('storage/banner2.jpg'),
            asset('storage/banner3.jpg'),
        ];

        return view('welcome-bw', compact('bestSellers', 'newArrivals', 'categories', 'featuredBrands', 'sliderBanners'));
    }

    /**
     * Handle contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleContactForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|min:10',
        ]);

        if ($validator->fails()) {
            return redirect()->route('contact')
                        ->withErrors($validator)
                        ->withInput();
        }

        $validatedData = $validator->validated();

        try {
            // Send the email
            // IMPORTANT: Replace '<EMAIL>' with your actual admin email address
            // or use a configuration value like config('mail.admin_address')
            Mail::to('<EMAIL>')->send(new ContactFormMail(
                $validatedData['name'],
                $validatedData['email'],
                $validatedData['subject'],
                $validatedData['message']
            ));

            return redirect()->route('contact')->with('success', 'Thank you for your message! We will get back to you soon.');

        } catch (\Exception $e) {
            // Log the error or handle it as needed
            // For now, redirect back with a generic error message
            // You might want to log $e->getMessage() for debugging
            report($e); // Helper function to log exceptions
            return redirect()->route('contact')
                        ->with('error', 'Sorry, there was an issue sending your message. Please try again later.')
                        ->withInput();
        }
    }
}
