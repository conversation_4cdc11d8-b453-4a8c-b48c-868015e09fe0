<?php

namespace App\Livewire\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Layout('layouts.app')]
class Login extends Component
{
    #[Validate('required|string|email')]
    public string $email = '';

    #[Validate('required|string')]
    public string $password = '';

    public bool $remember = false;

    /**
     * Handle an incoming authentication request.
     *
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function login()
    {
        // Trim whitespace from inputs
        $this->email = trim($this->email);
        $this->password = trim($this->password);

        // Validate with custom messages for better user experience
        $this->validate([
            'email' => 'required|string|email',
            'password' => 'required|string|min:1',
        ], [
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'password.required' => 'Please enter your password.',
        ]);

        $this->ensureIsNotRateLimited();

        if (! Auth::attempt(['email' => $this->email, 'password' => $this->password], $this->remember)) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'email' => __('auth.failed'),
            ]);
        }

        RateLimiter::clear($this->throttleKey());
        Session::regenerate();

        // Add success message
        session()->flash('success', 'You have successfully logged in!');

        // CRITICAL FIX: Enhanced redirect logic with vendor onboarding check and debugging
        $user = auth()->user();
        $redirectRoute = 'dashboard';

        // DEBUGGING: Log user details for troubleshooting
        \Log::info('Login redirect debug', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'is_vendor' => $user->isVendor(),
            'is_admin' => $user->isAdmin(),
            'role_id' => $user->role_id,
            'role_name' => $user->role?->name,
        ]);

        if ($user->isVendor()) {
            $vendor = $user->vendor;

            // DEBUGGING: Log vendor details
            \Log::info('Vendor redirect debug', [
                'vendor_exists' => $vendor ? true : false,
                'vendor_id' => $vendor?->id,
                'has_completed_onboarding' => $vendor?->has_completed_onboarding,
                'is_approved' => $vendor?->is_approved,
            ]);

            // Check if vendor needs to complete onboarding
            if ($vendor && !$vendor->has_completed_onboarding) {
                $redirectRoute = 'vendor.onboarding';
            } elseif ($vendor && !$vendor->is_approved) {
                $redirectRoute = 'vendor.pending';
            } else {
                $redirectRoute = 'vendor.dashboard';
            }
        } elseif ($user->isAdmin()) {
            $redirectRoute = 'admin.dashboard';
        }

        // DEBUGGING: Log final redirect decision
        \Log::info('Login redirect decision', [
            'user_id' => $user->id,
            'redirect_route' => $redirectRoute,
            'route_url' => route($redirectRoute),
        ]);

        // CRITICAL FIX: Use correct Livewire redirect method based on official docs
        try {
            // Primary approach: Use redirectRoute for named routes (recommended)
            $this->redirectRoute($redirectRoute);
        } catch (\Exception $e) {
            \Log::error('Livewire redirectRoute failed, trying alternative', [
                'error' => $e->getMessage(),
                'redirect_route' => $redirectRoute,
                'user_id' => $user->id,
            ]);

            // Fallback approach: Use basic redirect with URL
            $this->redirect(route($redirectRoute));
        }
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->email).'|'.request()->ip());
    }
}
