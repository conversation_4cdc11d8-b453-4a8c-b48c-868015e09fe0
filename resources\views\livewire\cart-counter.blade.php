<div>
    <a href="{{ route('cart.index') }}"
       class="group relative p-3 text-gray-600 hover:text-black transition-all duration-300 hover:scale-110 rounded-xl hover:bg-gray-100 min-w-[48px] min-h-[48px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50"
       title="Shopping Cart ({{ $count }} {{ $count === 1 ? 'item' : 'items' }})">
        <div class="relative">
            <i class="text-xl sm:text-2xl fa-solid fa-shopping-cart transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"></i>
            @if ($count > 0)
                <span class="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 text-xs sm:text-sm font-bold text-white bg-gradient-to-r from-red-500 to-pink-500 rounded-full shadow-lg border-2 border-white animate-bounce">
                    {{ $count > 99 ? '99+' : $count }}
                </span>
            @endif
        </div>
        @if ($count > 0)
            <span class="sr-only">{{ $count }} {{ $count === 1 ? 'item' : 'items' }} in cart</span>
        @else
            <span class="sr-only">Cart is empty</span>
        @endif
    </a>
</div>
