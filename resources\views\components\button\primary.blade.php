{{-- Primary Button Component --}}
@props([
    'type' => 'button',
    'size' => 'md',
    'loading' => false,
    'disabled' => false,
    'href' => null,
    'wire:click' => null,
    'wire:target' => null,
    'icon' => null,
    'iconPosition' => 'left'
])

@php
    $baseClasses = 'inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 shadow-lg hover:shadow-xl';
    
    $sizeClasses = [
        'sm' => 'px-4 py-2 text-sm min-h-[40px]',
        'md' => 'px-6 py-3 text-base min-h-[44px]',
        'lg' => 'px-8 py-4 text-lg min-h-[48px]',
        'xl' => 'px-10 py-5 text-xl min-h-[52px]'
    ];
    
    $colorClasses = 'bg-gradient-to-r from-gray-900 to-black text-white hover:from-black hover:to-gray-800';
    
    $classes = $baseClasses . ' ' . $sizeClasses[$size] . ' ' . $colorClasses;
    
    if ($loading || $disabled) {
        $classes .= ' opacity-50 cursor-not-allowed transform-none hover:scale-100';
    }
    
    $wireAttributes = [];
    if (isset($attributes['wire:click'])) {
        $wireAttributes['wire:click'] = $attributes['wire:click'];
    }
    if (isset($attributes['wire:target'])) {
        $wireAttributes['wire:target'] = $attributes['wire:target'];
    }
    if ($loading) {
        $wireAttributes['wire:loading.attr'] = 'disabled';
    }
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        @if($icon && $iconPosition === 'left')
            <i class="{{ $icon }} mr-2"></i>
        @endif
        
        {{ $slot }}
        
        @if($icon && $iconPosition === 'right')
            <i class="{{ $icon }} ml-2"></i>
        @endif
    </a>
@else
    <button 
        type="{{ $type }}" 
        {{ $attributes->merge(['class' => $classes])->merge($wireAttributes) }}
        @if($disabled || $loading) disabled @endif>
        
        @if($loading)
            <span wire:loading.remove @if(isset($attributes['wire:target'])) wire:target="{{ $attributes['wire:target'] }}" @endif class="flex items-center">
                @if($icon && $iconPosition === 'left')
                    <i class="{{ $icon }} mr-2"></i>
                @endif
                {{ $slot }}
                @if($icon && $iconPosition === 'right')
                    <i class="{{ $icon }} ml-2"></i>
                @endif
            </span>
            
            <span wire:loading @if(isset($attributes['wire:target'])) wire:target="{{ $attributes['wire:target'] }}" @endif class="flex items-center">
                <svg class="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading...
            </span>
        @else
            @if($icon && $iconPosition === 'left')
                <i class="{{ $icon }} mr-2"></i>
            @endif
            {{ $slot }}
            @if($icon && $iconPosition === 'right')
                <i class="{{ $icon }} ml-2"></i>
            @endif
        @endif
    </button>
@endif
