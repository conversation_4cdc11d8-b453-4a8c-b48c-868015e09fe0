<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;
    // SECURITY FIX: Restrict mass assignment for order security
    protected $fillable = [
        'user_id',
        'vendor_id',
        'order_number',
        'status',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'total_amount',
        'total',
        'currency',
        'shipping_name',
        'shipping_email',
        'shipping_phone',
        'shipping_address',
        'shipping_city',
        'shipping_state',
        'shipping_country',
        'shipping_postal_code',
        'shipping_lga',
        'shipping_method',
        'shipping_cost',
        'shipbubble_token',
        'shipping_courier_id',
        'shipping_service_code',
        'shipping_tracking_url',
        'shipping_provider_order_id',
        'payment_method',
        'payment_status',
        'payment_reference',
        'billing_address',
        'shipped_at',
        'delivered_at',
    ];

    // SECURITY FIX: Protect critical fields from mass assignment
    protected $guarded = [
        'id',
        'created_at',
        'updated_at',
    ];
    
    protected $casts = [
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
    ];
    
    /**
     * Boot function to handle order events
     */
    protected static function boot()
    {
        parent::boot();
        
        // Auto-generate order number when creating a new order
        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = 'ORD-' . uniqid();
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
    
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function getStatusClassAttribute()
    {
        switch ($this->status) {
            case 'completed':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'processing':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'shipped':
                return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
            case 'cancelled':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
            case 'pending':
            default:
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        }
    }
    
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }
    
    public function isPending()
    {
        return $this->status === 'pending';
    }
    
    public function isProcessing()
    {
        return $this->status === 'processing';
    }
    
    public function isCompleted()
    {
        return $this->status === 'completed';
    }
    
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }
    
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    public function getAddressAttribute()
    {
        return $this->shipping_name;
    }

    public function getCityAttribute()
    {
        return $this->shipping_city;
    }

    public function getLgaAttribute()
    {
        return $this->shipping_lga;
    }

    public function getStateAttribute()
    {
        return $this->shipping_state;
    }

    public function getPostalCodeAttribute()
    {
        return $this->shipping_postal_code;
    }

    /**
     * Get formatted shipping address - handles both JSON and individual fields
     */
    public function getFormattedShippingAddressAttribute()
    {
        // If we have individual shipping fields, use them (preferred)
        if ($this->shipping_name || $this->shipping_city || $this->shipping_state) {
            return [
                'name' => $this->shipping_name,
                'address' => $this->shipping_address && !$this->isJsonString($this->shipping_address)
                    ? $this->shipping_address
                    : null,
                'city' => $this->shipping_city,
                'state' => $this->shipping_state,
                'postal_code' => $this->shipping_postal_code,
                'country' => $this->shipping_country,
                'phone' => $this->shipping_phone,
                'lga' => $this->shipping_lga,
            ];
        }

        // If shipping_address contains JSON, parse it
        if ($this->shipping_address && $this->isJsonString($this->shipping_address)) {
            $decoded = json_decode($this->shipping_address, true);
            if ($decoded && is_array($decoded)) {
                return [
                    'name' => $decoded['name'] ?? $decoded['first_name'] . ' ' . ($decoded['last_name'] ?? ''),
                    'address' => $decoded['address'] ?? $decoded['address_line_1'] ?? null,
                    'city' => $decoded['city'] ?? null,
                    'state' => $decoded['state'] ?? null,
                    'postal_code' => $decoded['postal_code'] ?? $decoded['zip'] ?? null,
                    'country' => $decoded['country'] ?? 'Nigeria',
                    'phone' => $decoded['phone'] ?? null,
                    'lga' => $decoded['lga'] ?? null,
                ];
            }
        }

        // Fallback to basic shipping_address as string
        return [
            'name' => null,
            'address' => $this->shipping_address,
            'city' => null,
            'state' => null,
            'postal_code' => null,
            'country' => null,
            'phone' => null,
            'lga' => null,
        ];
    }

    /**
     * Get formatted billing address - handles both JSON and individual fields
     */
    public function getFormattedBillingAddressAttribute()
    {
        // If billing_address contains JSON, parse it
        if ($this->billing_address && $this->isJsonString($this->billing_address)) {
            $decoded = json_decode($this->billing_address, true);
            if ($decoded && is_array($decoded)) {
                return [
                    'name' => $decoded['name'] ?? $decoded['first_name'] . ' ' . ($decoded['last_name'] ?? ''),
                    'address' => $decoded['address'] ?? $decoded['address_line_1'] ?? null,
                    'city' => $decoded['city'] ?? null,
                    'state' => $decoded['state'] ?? null,
                    'postal_code' => $decoded['postal_code'] ?? $decoded['zip'] ?? null,
                    'country' => $decoded['country'] ?? 'Nigeria',
                    'phone' => $decoded['phone'] ?? null,
                ];
            }
        }

        // Fallback to basic billing_address as string or same as shipping
        return [
            'name' => null,
            'address' => $this->billing_address ?: 'Same as shipping address',
            'city' => null,
            'state' => null,
            'postal_code' => null,
            'country' => null,
            'phone' => null,
        ];
    }

    /**
     * Check if a string is valid JSON
     */
    private function isJsonString($string)
    {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Get a single line shipping address for display
     */
    public function getShippingAddressLineAttribute()
    {
        $address = $this->formatted_shipping_address;

        $parts = array_filter([
            $address['address'],
            $address['city'],
            $address['state'],
            $address['postal_code'],
            $address['country']
        ]);

        return implode(', ', $parts) ?: 'Address not available';
    }

    /**
     * Get a single line billing address for display
     */
    public function getBillingAddressLineAttribute()
    {
        $address = $this->formatted_billing_address;

        $parts = array_filter([
            $address['address'],
            $address['city'],
            $address['state'],
            $address['postal_code'],
            $address['country']
        ]);

        return implode(', ', $parts) ?: 'Same as shipping address';
    }

    public function getStatusColorAttribute()
    {
        return [
            'pending' => 'warning',
            'processing' => 'info',
            'shipped' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger',
        ][$this->status] ?? 'secondary';
    }

    /**
     * Get formatted total with Naira symbol
     */
    public function getFormattedTotalAttribute()
    {
        return '₦' . number_format($this->total, 2);
    }

    /**
     * Get formatted shipping cost with Naira symbol
     */
    public function getFormattedShippingCostAttribute()
    {
        return '₦' . number_format($this->shipping_cost, 2);
    }

    /**
     * Get formatted subtotal with Naira symbol
     */
    public function getFormattedSubtotalAttribute()
    {
        $subtotal = $this->items->sum(function ($item) {
            return $item->price * $item->quantity;
        });
        return '₦' . number_format($subtotal, 2);
    }

    /**
     * Get formatted discount with Naira symbol
     */
    public function getFormattedDiscountAttribute()
    {
        // Assuming there's a discount_amount field or calculate from discount_code
        $discount = $this->discount_amount ?? 0;
        return '₦' . number_format($discount, 2);
    }
}
