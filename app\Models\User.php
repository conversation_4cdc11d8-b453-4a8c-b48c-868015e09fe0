<?php
namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    // SECURITY FIX: Restrict mass assignment to safe fields only
    protected $fillable = [
        'name',
        'email',
        'password',
        'email_verified_at',
        'phone',
        'address',
        'city',
        'state',
        'country',
    ];

    // SECURITY FIX: Protect sensitive fields from mass assignment
    protected $guarded = [
        'role_id',
        'remember_token',
        'id',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'role_id' => 'integer',
    ];

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function isAdmin()
    {
        // SECURITY FIX: Use role name instead of hard-coded ID
        return $this->hasRole('admin');
    }

    public function isVendor()
    {
        // SECURITY FIX: Use role name instead of hard-coded ID
        return $this->hasRole('vendor');
    }
    
    public function vendor()
    {
        return $this->hasOne(Vendor::class);
    }
    
    public function isApprovedVendor()
    {
        return $this->isVendor() && $this->vendor && $this->vendor->is_approved;
    }

    /**
     * SECURITY FIX: Enhanced role checking with proper validation
     */
    public function hasRole(string $roleName): bool
    {
        // SECURITY FIX: Validate role name input
        if (empty($roleName) || !is_string($roleName)) {
            return false;
        }

        // SECURITY FIX: Sanitize role name
        $roleName = strtolower(trim($roleName));

        // SECURITY FIX: Validate against allowed roles
        $allowedRoles = ['admin', 'vendor', 'user'];
        if (!in_array($roleName, $allowedRoles)) {
            return false;
        }

        // Primary check: Use relationship if available
        if ($this->relationLoaded('role') && $this->role) {
            return strtolower($this->role->name) === $roleName;
        }

        // Load role if not already loaded
        if (!$this->relationLoaded('role')) {
            $this->load('role');
        }

        // Check role name if role exists
        if ($this->role) {
            return strtolower($this->role->name) === $roleName;
        }

        // SECURITY FIX: No fallback to hard-coded IDs
        return false;
    }
    
    public function wishlist()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function wishlistProducts()
    {
        return $this->belongsToMany(Product::class, 'wishlists');
    }
    
    public function orders()
    {
        return $this->hasMany(Order::class);
    }
    
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the user's most recent shipping address from their latest order.
     */
    public function shippingAddress()
    {
        return $this->hasOne(Order::class)->latestOfMany();
    }
    
    /**
     * Get user's initials.
     *
     * @return string
     */
    public function initials()
    {
        $name = $this->name;
        $words = explode(' ', $name);
        $initials = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }
        
        return $initials ?: strtoupper(substr($name, 0, 1));
    }

    /**
     * SECURITY FIX: Check if user can access admin features
     */
    public function canAccessAdmin(): bool
    {
        return $this->isAdmin();
    }

    /**
     * SECURITY FIX: Check if user can access vendor features
     */
    public function canAccessVendor(): bool
    {
        return $this->isVendor() && $this->isApprovedVendor();
    }

    /**
     * SECURITY FIX: Check if user has any elevated privileges
     */
    public function hasElevatedPrivileges(): bool
    {
        return $this->isAdmin() || $this->isVendor();
    }

    /**
     * SECURITY FIX: Get user's role name safely
     */
    public function getRoleName(): string
    {
        if ($this->role) {
            return $this->role->name;
        }

        return 'user'; // Default role
    }
}

