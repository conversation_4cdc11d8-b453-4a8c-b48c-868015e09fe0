<?php

namespace App\Services\Cart;

use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Session\SessionManager;

class CartService
{
    protected $session;
    protected $cart;

    /**
     * CartService constructor.
     */
    public function __construct()
    {
        $this->session = app(SessionManager::class);
        $this->cart = $this->session->get('cart', new Collection());
    }

    /**
     * Add an item to the cart.
     *
     * @param Product $product
     * @param int $quantity
     * @param ProductVariant|null $variant
     * @return void
     */
    public function add(Product $product, int $quantity = 1, $variant = null): void
    {
        // Create unique cart key for product + variant combination
        $cartKey = $this->getCartKey($product->id, $variant?->id);
        $cartItem = $this->cart->get($cartKey);

        if (!$cartItem) {
            $this->cart->put($cartKey, [
                'product_id' => $product->id,
                'variant_id' => $variant?->id,
                'name' => $product->name,
                'variant_name' => $variant?->display_name,
                'price' => $variant ? $variant->price : $product->getCurrentPrice(),
                'quantity' => $quantity,
                'image_url' => $variant ? $variant->image_url : $product->image_url,
                'vendor_id' => $product->vendor_id,
                'sku' => $variant?->sku ?? $product->sku,
            ]);
        } else {
            $cartItem['quantity'] += $quantity;
            $this->cart->put($cartKey, $cartItem);
        }

        $this->session->put('cart', $this->cart);
    }

    /**
     * Generate unique cart key for product and variant combination
     */
    private function getCartKey(int $productId, ?int $variantId = null): string
    {
        return $variantId ? "product_{$productId}_variant_{$variantId}" : "product_{$productId}";
    }

    /**
     * Update the quantity of an item in the cart.
     *
     * @param string $cartKey
     * @param int $quantity
     * @return void
     */
    public function update(string $cartKey, int $quantity): void
    {
        $cartItem = $this->cart->get($cartKey);

        if ($cartItem) {
            $cartItem['quantity'] = $quantity;
            $this->cart->put($cartKey, $cartItem);
            $this->session->put('cart', $this->cart);
        }
    }

    /**
     * Remove an item from the cart.
     *
     * @param string $cartKey
     * @return void
     */
    public function remove(string $cartKey): void
    {
        $this->cart->forget($cartKey);
        $this->session->put('cart', $this->cart);
    }

    /**
     * Update cart item by product and variant IDs (for backward compatibility)
     */
    public function updateByIds(int $productId, int $quantity, ?int $variantId = null): void
    {
        $cartKey = $this->getCartKey($productId, $variantId);
        $this->update($cartKey, $quantity);
    }

    /**
     * Remove cart item by product and variant IDs (for backward compatibility)
     */
    public function removeByIds(int $productId, ?int $variantId = null): void
    {
        $cartKey = $this->getCartKey($productId, $variantId);
        $this->remove($cartKey);
    }

    /**
     * Clear the cart.
     *
     * @return void
     */
    public function clear(): void
    {
        $this->cart = new Collection();
        $this->session->put('cart', $this->cart);
    }

    /**
     * Get the cart contents.
     *
     * @return Collection
     */
    public function content(): Collection
    {
        return $this->cart;
    }

    /**
     * Get the number of items in the cart.
     *
     * @return int
     */
    public function count(): int
    {
        return $this->cart->sum('quantity');
    }

    /**
     * Get the total price of the items in the cart.
     *
     * @return float
     */
    public function total(): float
    {
        return $this->cart->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });
    }

    /**
     * Get the subtotal price of the items in the cart (without tax).
     *
     * @return float
     */
    public function subtotal(): float
    {
        return $this->total();
    }

    /**
     * Get the tax amount for the items in the cart.
     *
     * @param float $taxRate
     * @return float
     */
    public function tax(float $taxRate = 0.08): float
    {
        return $this->subtotal() * $taxRate;
    }

    /**
     * PRICE VALIDATION FIX: Validate all cart prices against current database prices
     */
    public function validatePrices(): array
    {
        $errors = [];
        $priceChanges = [];

        foreach ($this->cart as $productId => $item) {
            $product = \App\Models\Product::find($productId);

            if (!$product) {
                $errors[] = "Product '{$item['name']}' is no longer available";
                continue;
            }

            if (!$product->is_active) {
                $errors[] = "Product '{$product->name}' is no longer available";
                continue;
            }

            // Get current price from database
            $currentPrice = $product->getCurrentPrice();
            $cartPrice = (float) $item['price'];

            // Allow small floating point differences (1 kobo)
            $priceDifference = abs($currentPrice - $cartPrice);

            if ($priceDifference > 0.01) {
                $priceChanges[] = [
                    'product_id' => $productId,
                    'product_name' => $product->name,
                    'old_price' => $cartPrice,
                    'new_price' => $currentPrice,
                    'difference' => $currentPrice - $cartPrice
                ];
            }

            // Check stock availability
            if (!$product->hasSufficientStock($item['quantity'])) {
                $availableStock = $product->getStockLevel();
                $errors[] = "Only {$availableStock} units of '{$product->name}' are available";
            }
        }

        return [
            'errors' => $errors,
            'price_changes' => $priceChanges,
            'is_valid' => empty($errors) && empty($priceChanges)
        ];
    }

    /**
     * PRICE VALIDATION FIX: Update cart prices to current database prices
     */
    public function updatePrices(): bool
    {
        $updated = false;

        foreach ($this->cart as $productId => $item) {
            $product = \App\Models\Product::find($productId);

            if ($product && $product->is_active) {
                $currentPrice = $product->getCurrentPrice();
                $cartPrice = (float) $item['price'];

                if (abs($currentPrice - $cartPrice) > 0.01) {
                    $this->cart[$productId]['price'] = $currentPrice;
                    $updated = true;

                    // Log price updates for audit trail
                    \Log::info('Cart price updated', [
                        'product_id' => $productId,
                        'product_name' => $product->name,
                        'old_price' => $cartPrice,
                        'new_price' => $currentPrice,
                        'user_id' => auth()->id(),
                        'session_id' => session()->getId()
                    ]);
                }
            }
        }

        if ($updated) {
            $this->session->put('cart', $this->cart);
        }

        return $updated;
    }
}
