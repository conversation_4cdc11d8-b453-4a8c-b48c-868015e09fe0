<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use App\Models\Vendor;

class DashboardController extends Controller
{
    public function index()
    {
        // CRITICAL FIX: Add error handling for dashboard metrics
        try {
            // Calculate key metrics
            $totalSales = Order::where('status', '!=', 'cancelled')->sum('total') ?? 0;
            $totalOrders = Order::count() ?? 0;
            $totalCustomers = User::whereDoesntHave('vendor')->count() ?? 0; // Users who are not vendors
            $totalVendors = Vendor::count() ?? 0;

        // Get pending vendors for approval
        $pendingVendors = Vendor::where('is_approved', false)
            ->with('user')
            ->latest()
            ->limit(5)
            ->get();

        // Get recent orders with user information
        $recentOrders = Order::with('user')
            ->latest()
            ->limit(5)
            ->get();

            return view('admin.dashboard', compact(
                'totalSales',
                'totalOrders',
                'totalCustomers',
                'totalVendors',
                'pendingVendors',
                'recentOrders'
            ));
        } catch (\Exception $e) {
            // Log the error and provide fallback data
            \Log::error('Admin dashboard error: ' . $e->getMessage());

            return view('admin.dashboard', [
                'totalSales' => 0,
                'totalOrders' => 0,
                'totalCustomers' => 0,
                'totalVendors' => 0,
                'pendingVendors' => collect(),
                'recentOrders' => collect()
            ])->with('error', 'Some dashboard data could not be loaded. Please refresh the page.');
        }
    }
}
