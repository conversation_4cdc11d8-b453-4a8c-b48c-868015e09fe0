<?php

use App\Helpers\ArrayHelper;
use Illuminate\Support\Facades\Log;

if (!function_exists('safe_array_key_exists')) {
    /**
     * Safe version of array_key_exists that handles non-array inputs
     */
    function safe_array_key_exists($key, $array)
    {
        return ArrayHelper::safeArrayKeyExists($key, $array);
    }
}

if (!function_exists('safe_array_get')) {
    /**
     * Safe array access with default value
     */
    function safe_array_get($array, $key, $default = null)
    {
        return ArrayHelper::safeGet($array, $key, $default);
    }
}

if (!function_exists('ensure_array')) {
    /**
     * Ensure a value is an array
     */
    function ensure_array($value, $default = [])
    {
        return ArrayHelper::ensureArray($value, $default);
    }
}

// Debug helper for array operations (only in development and when safe to call)
if (!function_exists('debug_array_key_exists')) {
    function debug_array_key_exists($key, $array, $context = 'unknown')
    {
        if (!is_array($array)) {
            // Only log if <PERSON><PERSON> is available and we're in development
            if (function_exists('app') && app()->bound('log') && config('app.debug', false)) {
                try {
                    Log::warning('debug_array_key_exists called with non-array', [
                        'key' => $key,
                        'array_type' => gettype($array),
                        'context' => $context
                    ]);
                } catch (\Exception $e) {
                    // Silently fail if logging is not available
                }
            }
            return false;
        }

        return array_key_exists($key, $array);
    }
}
