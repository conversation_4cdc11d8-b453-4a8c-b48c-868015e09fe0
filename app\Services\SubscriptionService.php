<?php

namespace App\Services;

use App\Models\Vendor;
use App\Models\VendorSubscription;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;

class SubscriptionService
{
    /**
     * Check if vendor can receive new orders
     */
    public function canReceiveOrders(Vendor $vendor): bool
    {
        // Check if vendor is approved first
        if (!$vendor->is_approved) {
            return false;
        }

        // Check vendor status - allow 'active' or null/empty (for backward compatibility)
        if ($vendor->status && !in_array($vendor->status, ['active', 'approved'])) {
            return false;
        }

        $subscription = $vendor->subscription;

        // If a subscription exists, its status is the source of truth.
        if ($subscription) {
            return $subscription->status === 'active' && $subscription->ends_at > now();
        }

        // If no subscription, fall back to the 10-order free trial.
        $orderCount = $vendor->orders()->count();
        if ($orderCount < 10) {
            return true;
        }

        // If no subscription and free trial is over, they cannot receive orders.
        return false;
    }
    
    /**
     * Get vendor's subscription status
     */
    public function getSubscriptionStatus(Vendor $vendor): array
    {
        $orderCount = $vendor->orders()->count();
        $subscription = $vendor->subscription;
        
        if ($orderCount < 10) {
            return [
                'status' => 'free_trial',
                'orders_remaining' => 10 - $orderCount,
                'message' => 'You have ' . (10 - $orderCount) . ' free orders remaining.',
                'can_receive_orders' => true
            ];
        }
        
        if (!$subscription || $subscription->status !== 'active') {
            return [
                'status' => 'subscription_required',
                'orders_remaining' => 0,
                'message' => 'Your free trial has ended. Please subscribe to continue receiving orders.',
                'can_receive_orders' => false
            ];
        }
        
        if ($subscription->ends_at < now()) {
            return [
                'status' => 'subscription_expired',
                'orders_remaining' => 0,
                'message' => 'Your subscription has expired. Please renew to continue receiving orders.',
                'can_receive_orders' => false
            ];
        }
        
        $daysRemaining = now()->diffInDays($subscription->ends_at, false);
        
        return [
            'status' => 'active',
            'orders_remaining' => 'unlimited',
            'message' => 'Your subscription is active. ' . max(0, $daysRemaining) . ' days remaining.',
            'can_receive_orders' => true,
            'days_remaining' => max(0, $daysRemaining)
        ];
    }
    
    /**
     * Create or update the standard subscription plan
     */
    public function ensureStandardPlan(): SubscriptionPlan
    {
        return SubscriptionPlan::updateOrCreate(
            ['name' => 'Monthly Plan'],
            [
                'price' => 9000.00, // ₦9,000 (stored as decimal naira)
                'interval' => 'monthly',
                'duration_days' => 30,
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                    'Marketing tools',
                    'ShipBubble integration'
                ]),
                'order_limit' => null, // Unlimited
                'status' => 'active'
            ]
        );
    }

    /**
     * Ensure all subscription plans are created
     */
    public function ensureAllPlans(): void
    {
        // Monthly Plan - CRITICAL FIX: Store as decimal naira, not kobo
        SubscriptionPlan::updateOrCreate(
            ['name' => 'Monthly Plan'],
            [
                'price' => 9000.00, // ₦9,000 (stored as decimal naira)
                'interval' => 'monthly',
                'duration_days' => 30,
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                    'Marketing tools',
                    'ShipBubble integration'
                ]),
                'order_limit' => null,
                'status' => 'active'
            ]
        );

        // Bi-Annual Plan - CRITICAL FIX: Store as decimal naira, not kobo
        SubscriptionPlan::updateOrCreate(
            ['name' => 'Bi-Annual Plan'],
            [
                'price' => 8700.00, // ₦8,700 per month (stored as decimal naira)
                'interval' => 'bi-annually',
                'duration_days' => 180,
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                    'Marketing tools',
                    'ShipBubble integration',
                    '3% discount (₦8,700/month)'
                ]),
                'order_limit' => null,
                'status' => 'active'
            ]
        );

        // Annual Plan - CRITICAL FIX: Store as decimal naira, not kobo
        SubscriptionPlan::updateOrCreate(
            ['name' => 'Annual Plan'],
            [
                'price' => 8500.00, // ₦8,500 per month (stored as decimal naira)
                'interval' => 'annually',
                'duration_days' => 365,
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                    'Marketing tools',
                    'ShipBubble integration',
                    '6% discount (₦8,500/month)',
                    'Free setup assistance'
                ]),
                'order_limit' => null,
                'status' => 'active'
            ]
        );
    }
    
    /**
     * Block vendor from receiving new orders
     */
    public function blockVendorOrders(Vendor $vendor): void
    {
        $vendor->update(['status' => 'suspended']);

        // Optionally, dispatch an event or send a notification to the vendor.
        // event(new VendorSuspended($vendor));
    }

    /**
     * Upgrade or downgrade vendor subscription
     */
    public function changePlan(Vendor $vendor, SubscriptionPlan $newPlan): bool
    {
        $currentSubscription = $vendor->subscription;

        if (!$currentSubscription || $currentSubscription->status !== 'active') {
            return false;
        }

        // Calculate prorated amount if upgrading mid-cycle
        $daysRemaining = $currentSubscription->ends_at->diffInDays(now());
        $totalDays = $currentSubscription->starts_at->diffInDays($currentSubscription->ends_at);
        $proratedRatio = $daysRemaining / $totalDays;

        // Deactivate current subscription
        $currentSubscription->update(['status' => 'inactive']);

        // Create new subscription
        $newSubscription = VendorSubscription::create([
            'vendor_id' => $vendor->id,
            'subscription_plan_id' => $newPlan->id,
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addDays($newPlan->duration_days ?? 30),
        ]);

        return true;
    }

    /**
     * Cancel vendor subscription
     */
    public function cancelSubscription(Vendor $vendor): bool
    {
        $subscription = $vendor->subscription;

        if (!$subscription || $subscription->status !== 'active') {
            return false;
        }

        // If there's a Paystack subscription, cancel it
        if ($subscription->paystack_subscription_code) {
            $paystackService = app(PaystackService::class);
            $result = $paystackService->cancelSubscription($subscription->paystack_subscription_code);

            if (!$result['status']) {
                return false;
            }
        }

        // Update subscription status
        $subscription->update([
            'status' => 'cancelled',
            'ends_at' => now(), // End immediately or keep until end of billing period
        ]);

        return true;
    }

    /**
     * Handle failed payment
     */
    public function handleFailedPayment(Vendor $vendor): void
    {
        $subscription = $vendor->subscription;

        if ($subscription) {
            $subscription->update(['status' => 'past_due']);

            // Give vendor 3 days grace period
            $subscription->update(['ends_at' => now()->addDays(3)]);

            // Send notification to vendor about failed payment
            // event(new SubscriptionPaymentFailed($vendor, $subscription));
        }
    }

    /**
     * Reactivate subscription after successful payment
     */
    public function reactivateSubscription(Vendor $vendor, SubscriptionPlan $plan): bool
    {
        $subscription = $vendor->subscription;

        if ($subscription && $subscription->status === 'past_due') {
            $subscription->update([
                'status' => 'active',
                'ends_at' => now()->addDays($plan->duration_days ?? 30),
            ]);

            return true;
        }

        return false;
    }
    
    /**
     * Send subscription reminder notifications.
     * This method should be called by a scheduled command.
     */
    public function sendSubscriptionReminders(): void
    {
        $thresholdDate = now()->addDays(7);

        $expiringSubscriptions = VendorSubscription::with('vendor.user')
            ->where('status', 'active')
            ->where('ends_at', '<=', $thresholdDate)
            ->where('ends_at', '>', now())
            ->with('vendor')
            ->get();
            
        foreach ($expiringSubscriptions as $subscription) {
            $vendor = $subscription->vendor;
            // Ensure the user exists to prevent errors
            if ($vendor && $vendor->user) {
                $vendor->user->notify(new \App\Notifications\VendorSubscriptionReminder($subscription));
            }
        }
    }
}
