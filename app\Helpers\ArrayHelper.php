<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Log;

class ArrayHelper
{
    /**
     * Safe version of array_key_exists that handles non-array inputs
     */
    public static function safeArrayKeyExists($key, $array)
    {
        if (!is_array($array)) {
            Log::warning('safeArrayKeyExists called with non-array', [
                'key' => $key,
                'array_type' => gettype($array),
                'array_value' => is_string($array) ? $array : 'non_string_value',
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
            ]);
            return false;
        }
        
        return array_key_exists($key, $array);
    }
    
    /**
     * Safe version of isset that works with arrays
     */
    public static function safeIsset($array, $key)
    {
        if (!is_array($array)) {
            return false;
        }
        
        return isset($array[$key]);
    }
    
    /**
     * Safe array access with default value
     */
    public static function safeGet($array, $key, $default = null)
    {
        if (!is_array($array)) {
            return $default;
        }
        
        return $array[$key] ?? $default;
    }
    
    /**
     * Validate and fix array structure
     */
    public static function ensureArray($value, $default = [])
    {
        if (!is_array($value)) {
            Log::warning('ensureArray called with non-array', [
                'value_type' => gettype($value),
                'value' => is_string($value) ? $value : 'non_string_value',
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
            return $default;
        }
        
        return $value;
    }
}
