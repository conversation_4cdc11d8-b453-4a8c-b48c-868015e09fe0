<?php

namespace App\Livewire\Product;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Services\Cart\CartService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;


class Options extends Component
{
    public Product $product;
    public $selectedVariantId;
    public $selectedColorId;
    public $selectedSizeId;
    public $quantity = 1;
    public $inWishlist = false;

    // Available options based on selections
    public $availableColors;
    public $availableSizes;

    // All possible variants for the product
    private $allVariants;

    public function mount(Product $product)
    {
        // EMERGENCY FIX: Add null checks and error handling
        if (!$product || !$product->exists) {
            throw new \Exception('Product not found or invalid');
        }

        $this->product = $product->load('variants.color', 'variants.size');

        // EMERGENCY FIX: Ensure variants relationship exists and is loaded
        $this->allVariants = $this->product->variants()->available()->with('color', 'size')->get();

        // EMERGENCY FIX: Initialize collections even if empty to prevent null errors
        $this->availableColors = $this->allVariants->map->color->unique()->filter() ?? collect();
        $this->availableSizes = $this->allVariants->map->size->unique()->filter() ?? collect();

        // CRITICAL FIX: If product has only one variant type, auto-select if there's only one option
        if ($this->availableColors->count() === 1 && $this->availableSizes->isEmpty()) {
            // Color-only product with one color
            $this->selectColor($this->availableColors->first()->id);
        } elseif ($this->availableSizes->count() === 1 && $this->availableColors->isEmpty()) {
            // Size-only product with one size
            $this->selectSize($this->availableSizes->first()->id);
        }

        $this->checkWishlistStatus();
    }

    public function selectColor($colorId)
    {
        $this->selectedColorId = $colorId;
        $this->selectedSizeId = null; // Reset size when color changes
        $this->selectedVariantId = null;

        // EMERGENCY FIX: Ensure allVariants is not null before using where()
        if (!$this->allVariants) {
            $this->allVariants = $this->product->variants()->available()->with('color', 'size')->get();
        }

        // Filter available sizes based on the selected color
        $this->availableSizes = $this->allVariants
            ->where('color_id', $this->selectedColorId)
            ->map->size
            ->unique()
            ->filter();

        // CRITICAL FIX: Check if this color has variants without sizes (color-only variants)
        $colorOnlyVariant = $this->allVariants->first(function ($variant) {
            return $variant->color_id == $this->selectedColorId && $variant->size_id === null;
        });

        if ($colorOnlyVariant) {
            // If there's a color-only variant, select it immediately
            $this->selectedVariantId = $colorOnlyVariant->id;
            $this->quantity = 1;
        } elseif ($this->availableSizes->count() === 1) {
            // If there's only one size for this color, auto-select it
            $this->selectSize($this->availableSizes->first()->id);
        }
    }

    public function selectSize($sizeId)
    {
        $this->selectedSizeId = $sizeId;

        // CRITICAL FIX: Find variant that matches the selection (handle both color+size and size-only variants)
        $variant = $this->allVariants->first(function ($variant) {
            // If we have both color and size selected
            if ($this->selectedColorId && $this->selectedSizeId) {
                return $variant->color_id == $this->selectedColorId && $variant->size_id == $this->selectedSizeId;
            }
            // If we only have size selected (size-only variants)
            elseif ($this->selectedSizeId && !$this->selectedColorId) {
                return $variant->size_id == $this->selectedSizeId && $variant->color_id === null;
            }
            return false;
        });

        if ($variant) {
            $this->selectedVariantId = $variant->id;
            $this->quantity = 1; // Reset quantity
        } else {
            $this->selectedVariantId = null;
        }
    }

    public function getSelectedVariantProperty()
    {
        if (!$this->selectedVariantId) {
            return null;
        }

        // Load variant with relationships to ensure consistency
        return ProductVariant::with(['color', 'size', 'product'])
            ->find($this->selectedVariantId);
    }

    public function checkWishlistStatus()
    {
        if (Auth::check()) {
            $this->inWishlist = Auth::user()->wishlist()->where('product_id', $this->product->id)->exists();
        }
    }

    public function toggleWishlist()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        if ($this->inWishlist) {
            Auth::user()->wishlist()->where('product_id', $this->product->id)->delete();
            $this->inWishlist = false;
            $this->dispatch('toast', message: 'Removed from wishlist.', type: 'info');
        } else {
            Auth::user()->wishlist()->create(['product_id' => $this->product->id]);
            $this->inWishlist = true;
            $this->dispatch('toast', message: 'Added to wishlist!', type: 'success');
        }
    }

    public function addToCart()
    {
        if (!Auth::check()) {
            $this->dispatch('toast', message: 'Please login to add items to your cart.', type: 'info');
            return $this->redirect(route('login'));
        }

        $variant = null;
        if ($this->product->variants->isNotEmpty()) {
            if (!$this->selectedVariantId) {
                $this->dispatch('toast', message: 'Please select a variant.', type: 'error');
                return;
            }
            $variant = $this->product->variants()->with(['size', 'color'])->find($this->selectedVariantId);

            // Ensure variant exists and is available
            if (!$variant) {
                $this->dispatch('toast', message: 'Selected variant is not available.', type: 'error');
                return;
            }

            if (!$variant->isAvailable()) {
                $this->dispatch('toast', message: 'Selected variant is not available.', type: 'error');
                return;
            }

            // Check stock
            if ($variant->stock_quantity < $this->quantity) {
                $this->dispatch('toast', message: 'Not enough stock available.', type: 'error');
                return;
            }
        } else {
            // Check product stock for non-variant products
            if ($this->product->stock < $this->quantity) {
                $this->dispatch('toast', message: 'Not enough stock available.', type: 'error');
                return;
            }
        }

        // Use CartService to add item
        $cartService = app(CartService::class);
        $cartService->add($this->product, $this->quantity, $variant);

        $this->dispatch('cartUpdated'); // For updating cart count in navbar, etc.

        $variantText = $variant ? " ({$variant->display_name})" : '';
        $this->dispatch('toast', message: "Product{$variantText} added to cart!", type: 'success');
    }

    public function incrementQuantity()
    {
        $stock = $this->selectedVariant ? $this->selectedVariant->stock_quantity : $this->product->stock;
        if ($this->quantity < $stock) {
            $this->quantity++;
        }
    }

    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
        }
    }

    public function render()
    {
        return view('livewire.product.options');
    }
}
